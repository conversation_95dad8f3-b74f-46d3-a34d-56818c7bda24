"use client";

import { useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useSession } from 'next-auth/react';
import { useCommunityBilling } from '@/contexts/CommunityBillingContext';

interface TrialExpiredRedirectProps {
  communitySlug: string;
}

/**
 * Component that redirects admins to billing page when trial expires
 * This serves as a backup to the middleware
 */
export default function TrialExpiredRedirect({ communitySlug }: TrialExpiredRedirectProps) {
  const router = useRouter();
  const { data: session } = useSession();
  const {
    trialActive,
    subscriptionActive,
    loading,
    billingData
  } = useCommunityBilling();

  useEffect(() => {
    // Only check for admins
    if (!session?.user?.id || loading) return;
    
    // Check if user is admin of this community
    const isAdmin = billingData && session.user.id === billingData._id;
    
    if (isAdmin) {
      // If no active trial and no active subscription, redirect to billing
      if (!trialActive && !subscriptionActive) {
        console.log('🔄 Trial expired, redirecting to billing page...');
        router.push(`/billing/${communitySlug}?expired=true`);
      }
    }
  }, [session, trialActive, subscriptionActive, loading, billingData, communitySlug, router]);

  // This component doesn't render anything
  return null;
}

/**
 * Hook version for easier integration
 */
export function useTrialExpiredRedirect(communitySlug: string) {
  const router = useRouter();
  const { data: session } = useSession();
  const {
    trialActive,
    subscriptionActive,
    loading,
    billingData
  } = useCommunityBilling();

  useEffect(() => {
    // Only check for admins
    if (!session?.user?.id || loading) return;
    
    // Check if user is admin of this community
    const isAdmin = billingData && session.user.id === billingData._id;
    
    if (isAdmin) {
      // If no active trial and no active subscription, redirect to billing
      if (!trialActive && !subscriptionActive) {
        console.log('🔄 Trial expired, redirecting to billing page...');
        router.push(`/billing/${communitySlug}?expired=true`);
      }
    }
  }, [session, trialActive, subscriptionActive, loading, billingData, communitySlug, router]);
}
